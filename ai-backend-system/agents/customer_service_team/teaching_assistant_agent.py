"""
定义教学助手 Agent (TeachingAssistantAgent) 类，该类继承自 AssistantAgent。

该 Agent 负责：
- 接收用户在企业微信 (WeCom) 上发送的关于 AI 教学的问题。
- 调用 LLM 模型 (通过配置的 LLM API) 生成 AI 教学内容以及回复学生问题。
- 调用 MultiUserMemory (通过配置的 Redis) 记录用户问题和 AI 教学内容，以及通过中长期记忆进行教学质量跟踪。
- 将生成的教学内容回复给用户。

核心业务流程包括：
- TeachingAssistantAgent接收来自services/wecom/message_handler.py的用户问题，范围仅限于python编程竞赛题目答疑。
- TeachingAssistantAgent会根据用户问题，判断是否需要直接解答或需要调用agents/dev_team/crawler_engineer_agent.py进行相关题目抓取后解答。
- 对于需要调用crawler engineer agent的情况，crawler engineer agent会调用crawler tool抓取相关题目信息，然后将结果返回给TeachingAssistantAgent。对于需要抓取的内容，目前仅限于洛谷python编程题目，比如题号类似：P1005，P1560等。
- 需要给学生创建各自的记忆能力（utils/memory/multi_user_memory.py），在回复解答时需要结合对于每位用户的记忆能力retrieve后回复，调用记忆能力的代码实现可以参考 utils/memory/examples/school_memory_system.py的用法；对于每位用户使用db_path模式进行隔离。
- 学生记忆管理原则：
    短期记忆：最多记录与学生最近7天的交流原始对话记录（如果学生信息包含图片则将图片中信息提炼出来作为文字记录）或最多不超过50条的聊天记录；超过上述阈值后，将老旧的记忆触发执行记忆压缩移至中期记忆；
    中期记忆：超过最近7天或超过最近50条记录后，将被压缩为中期记忆，中期记忆记录学生的错题类型，难以掌握的知识点，在交流中提炼对相关知识点的掌握程度；超过60天的中期记忆将被压缩为长期记忆；
    长期记忆：超过60天的中期记忆将被压缩为长期记忆，长期记忆记录学生的知识点掌握程度，知识点掌握程度的提升程度，知识点掌握程度的提升速度，知识点掌握程度的提升速度的提升程度等。
"""

import logging
import re
import os
import sys
import asyncio
import time
from datetime import datetime
from typing import Optional, Dict, Any, List

from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.tools import AgentTool
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_core import CancellationToken
from autogen_core.memory import MemoryContent, MemoryMimeType
from autogen_core.models import SystemMessage, UserMessage

# 使用相对导入以适应不同的Python路径设置
# 当PYTHONPATH包含ai-backend-system时
from config import get_config
from data_models import ContentType
from agents.dev_team.crawler_engineer_agent import CrawlerEngineerAgent, ParseType
from utils.memory.multi_user_memory import MultiUserMemory, MultiUserMemoryConfig, MemoryLevel

# 配置日志
logger = logging.getLogger(__name__)

class TeachingAssistantAgent(AssistantAgent):
    """教学助手代理，负责回答学生关于Python编程竞赛题目的问题

    特点：
    1. 支持从配置文件加载记忆压缩阈值设置
    2. 支持基于时间和消息数量的记忆压缩
    3. 自动记录用户消息计数，并在达到阈值时触发记忆压缩
    4. 提供详细的日志记录，便于调试和监控
    """

    def __init__(
        self,
        name: str = "teaching_assistant",
        system_message: str = """你是一位C++编程教师，负责回答学生的编程问题。

你的工作原则：
1. 简洁明了地回答问题，不做无关扩展
2. 确保每条回复不超过2048字节（约600-700中文字）
3. 使用正常、专业的语气，避免过度使用emoji或儿童化语言
4. 解释概念时使用清晰的比喻，但保持专业性
5. 提供简单易懂的代码示例，避免过于复杂的实现

回答格式要求：
1. 使用纯文本格式回复，不要使用Markdown格式
2. 不要使用```代码块标记，直接使用缩进和空行来展示代码
3. 不要使用粗体、斜体等Markdown格式，使用普通文本
4. 使用数字和字母编号来组织内容，如"1."、"2."、"a."、"b."等
5. 使用空行分隔段落，使文本易于阅读

回答编程问题时：
- 直接切入主题，简明扼要地解释核心概念
- 使用简单的类比帮助理解，但不过度简化
- 提供符合题目要求的伪代码或简单代码示例（使用缩进而非代码块标记）
- 分步骤解释时保持简洁
- 如果问题复杂，优先解释最关键的部分

当学生提到特定题目编号（如P1005）时，你会获取题目信息，并用清晰的语言解释题目要求。

记住：学生需要专业、清晰的指导，而非过度简化的解释。学生通过微信查看你的回复，微信不支持Markdown格式，所以必须使用纯文本格式。""",
        model_client: Optional[OpenAIChatCompletionClient] = None,
        crawler_agent: Optional[CrawlerEngineerAgent] = None,
    ) -> None:
        """初始化教学助手代理

        Args:
            name: 代理名称
            system_message: 系统提示消息
            model_client: 模型客户端，默认使用配置中指定的模型
            crawler_agent: 爬虫工程师代理，用于抓取题目信息
        """
        # 如果未提供模型客户端，则创建默认客户端
        if model_client is None:
            try:
                # 使用直接索引方式获取配置
                config = get_config()
                model_client = OpenAIChatCompletionClient(
                    model=config['agents']['teaching_assistant']['agent_model_name'],
                    api_key=config['agents']['teaching_assistant']['agent_model_api_key'],
                    model_info={
                        "vision": False,
                        "function_calling": True,
                        "json_output": True,
                        "family": "unknown",
                        "structured_output": True,
                    }
                )
            except KeyError as e:
                # 如果配置中没有相应的键，使用默认值
                logger.info(f"Configuration key not found in model client initialization: {e}, using default values")
                model_client = OpenAIChatCompletionClient(
                    model="gemini-2.0-flash",  # 默认模型
                    api_key=get_config()['api_keys']['gemini_api_key'],  # 使用通用API密钥
                    model_info={
                        "vision": False,
                        "function_calling": True,
                        "json_output": True,
                        "family": "unknown",
                        "structured_output": True,
                    }
                )
        self.model_client = model_client
        # 初始化爬虫代理
        self.crawler_agent = crawler_agent if crawler_agent else CrawlerEngineerAgent()

        # 创建爬虫工具
        crawler_tool = AgentTool(agent=self.crawler_agent)

        # 初始化记忆系统
        self.memory = self._init_memory_system()

        # 调用父类初始化
        super().__init__(
            name=name,
            system_message=system_message,
            model_client=model_client,
            tools=[crawler_tool],
            memory=[self.memory],
            reflect_on_tool_use=True
        )
        self._user_message_counts = {} # 用于在代理实例生命周期内跟踪每个用户的消息计数

        logger.info(f"TeachingAssistantAgent initialized with name: {name}")

    def _init_memory_system(self):
        """初始化记忆系统

        Returns:
            MultiUserMemory: 初始化的记忆系统实例
        """
        # 创建记忆系统基础路径
        base_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "../..", "data", "memories")
        os.makedirs(base_path, exist_ok=True)

        # 获取LLM客户端用于记忆压缩
        try:
            # 使用直接索引方式获取配置
            config = get_config()
            llm_client = OpenAIChatCompletionClient(
                model=config['agents']['memory_system']['agent_model_name'],
                api_key=config['agents']['memory_system']['agent_model_api_key'],
                model_info={
                    "vision": False,
                    "function_calling": True,
                    "json_output": True,
                    "family": "unknown",
                    "structured_output": True,
                }
            )
        except KeyError as e:
            # 如果配置中没有相应的键，使用默认值
            logger.warning(f"Configuration key not found in memory system LLM client: {e}, using default values")
            llm_client = OpenAIChatCompletionClient(
                model="gemini-2.0-pro",  # 默认模型，支持视觉功能
                api_key=get_config()['api_keys']['gemini_api_key'],  # 使用通用API密钥
                model_info={
                    "vision": True,
                    "function_calling": True,
                    "json_output": True,
                    "family": "unknown",
                    "structured_output": True,
                }
            )

        try:
            # 使用直接索引方式获取配置
            # 记忆压缩阈值配置 - 基于时间
            short_to_medium_time = config['agents']['memory_system']['compression_thresholds']['short_to_medium_time']
            medium_to_long_time = config['agents']['memory_system']['compression_thresholds']['medium_to_long_time']

            # 记忆压缩阈值配置 - 基于消息数量
            short_to_medium_count = config['agents']['memory_system']['compression_thresholds']['short_to_medium_count']
            medium_to_long_count = config['agents']['memory_system']['compression_thresholds']['medium_to_long_count']

            # 查询配置
            short_term_k = config['agents']['memory_system']['query_config']['short_term_k']
            medium_term_k = config['agents']['memory_system']['query_config']['medium_term_k']
            long_term_k = config['agents']['memory_system']['query_config']['long_term_k']

            # 维护设置
            maintenance_interval = config['agents']['memory_system']['maintenance_interval']
        except KeyError as e:
            # 如果配置中没有相应的键，使用默认值
            logger.warning(f"Configuration key not found: {e}, using default values")

            # 默认值
            short_to_medium_time = 7 * 24 * 60 * 60  # 7天
            medium_to_long_time = 60 * 24 * 60 * 60  # 60天
            short_to_medium_count = 20  # 20条消息
            medium_to_long_count = 50  # 50条消息
            short_term_k = 10
            medium_term_k = 5
            long_term_k = 3
            maintenance_interval = 5

        # 记录配置信息
        logger.info(f"Memory system configuration loaded from config file:")
        logger.info(f"  - Time thresholds: short_to_medium={short_to_medium_time}s, medium_to_long={medium_to_long_time}s")
        logger.info(f"  - Count thresholds: short_to_medium={short_to_medium_count}, medium_to_long={medium_to_long_count}")
        logger.info(f"  - Query config: short_term_k={short_term_k}, medium_term_k={medium_term_k}, long_term_k={long_term_k}")
        logger.info(f"  - Maintenance interval: {maintenance_interval}")

        # 创建记忆系统配置
        memory_config = MultiUserMemoryConfig(
            base_path=base_path,
            shared_memory_enabled=False,  # 不启用共享记忆
            user_isolation="db_path",  # 使用db_path模式进行用户隔离
            collection_name="teaching_assistant_memory",  # 设置集合名称

            # 记忆压缩设置
            enable_memory_compression=True,
            llm_client=llm_client,

            # 设置基于时间的记忆阈值
            short_to_medium_threshold=short_to_medium_time,
            medium_to_long_threshold=medium_to_long_time,

            # 设置基于消息数量的记忆阈值
            short_to_medium_count_threshold=short_to_medium_count,
            medium_to_long_count_threshold=medium_to_long_count,

            # 查询设置
            short_term_k=short_term_k,
            medium_term_k=medium_term_k,
            long_term_k=long_term_k,

            # 维护设置
            maintenance_interval=maintenance_interval
        )

        # 创建记忆系统
        memory = MultiUserMemory(memory_config)

        # 设置自定义压缩提示（同步方式）
        self._set_custom_compression_prompts_sync()

        logger.info("Memory system initialized")

        return memory

    def _set_custom_compression_prompts_sync(self) -> None:
        """设置自定义记忆压缩提示（同步版本）"""
        # 短期到中期记忆压缩提示
        short_to_medium_prompt = """
        你是一位教育记忆专家，负责分析学生的C++学习记录并提取关键信息。
        请分析以下学生的交流记录，并提取以下关键信息：
        1. 学生遇到的主要困难和常见错误
        2. 学生对哪些C++概念理解不清（如循环、变量、指针、引用、类等）
        3. 学生已经掌握的概念
        4. 学生的学习兴趣点和有效的解释方式
        5. 学生的学习进度和理解水平
        6. 哪些解释方式对该学生特别有效，尤其是对于C++特有的概念（如指针、内存管理）

        请简洁地总结这些信息，重点关注如何使C++概念更容易理解。
        """

        # 中期到长期记忆压缩提示
        medium_to_long_prompt = """
        你是一位教育发展专家，负责评估学生的C++学习进展。
        请分析以下学生的中期学习记录，并提取以下关键信息：
        1. 学生在C++学习中的长期兴趣变化
        2. 学生编程思维的发展情况，尤其是对于C++特有的思维方式
        3. 学生从简单概念到复杂概念的理解进步
        4. 学生在解决C++问题时的思考方式变化
        5. 学生对编程的信心和自主学习能力的发展
        6. 对该学生特别有效的教学方法

        请简洁地总结这些信息，重点关注如何保持学生对C++编程的兴趣和调整教学方法。
        """

        try:
            # 存储压缩提示，以便在异步方法中使用
            self._compression_prompts = {
                "short_to_medium": short_to_medium_prompt,
                "medium_to_long": medium_to_long_prompt
            }
            logger.info("Custom compression prompts prepared (will be applied when needed)")
        except Exception as e:
            logger.warning(f"Failed to prepare compression prompts: {e}")
            # 继续执行，不要因为设置压缩提示失败而中断初始化


    async def handle_user_message(self, user_id: str, content: str, image_data: Optional[bytes] = None,
                             image_type: Optional[str] = None, cancellation_token: Optional[CancellationToken] = None) -> str:
        """处理用户消息并生成回复

        Args:
            user_id: 用户ID（企业微信的external_userid）
            content: 用户消息内容
            image_data: 可选的图片数据
            image_type: 可选的图片类型
            cancellation_token: 取消令牌

        Returns:
            str: 回复内容
        """
        logger.info(f"Handling message from user {user_id}: {content[:50]}...")
        if image_data:
            logger.info(f"Message includes image of type: {image_type}")

        # 设置当前用户ID，以便记忆检索工具使用
        self.current_user_id = user_id

        # 获取用户专属记忆实例
        user_memory_instance = self.memory._get_user_memory(user_id)

        # 应用压缩提示到用户专属记忆实例
        if hasattr(user_memory_instance, 'update_compression_template'):
            try:
                user_memory_instance.update_compression_template(
                    "short_to_medium",
                    self._compression_prompts["short_to_medium"]
                )
                user_memory_instance.update_compression_template(
                    "medium_to_long",
                    self._compression_prompts["medium_to_long"]
                )
                logger.info(f"Custom compression templates updated for user {user_id}")
            except Exception as e:
                logger.warning(f"Failed to update compression templates for user {user_id}: {e}")
        else:
            logger.warning(f"User memory system for {user_id} does not support updating compression templates")

        # 如果有图片数据，处理图片内容
        image_content = None
        if image_data:
            try:
                # 使用外部服务或模型解析图片内容
                image_content = await self._process_image(image_data, image_type)
                logger.info(f"Extracted image content: {image_content[:100]}...")

                # 无论图片内容是什么，都使用统一的标记
                if content:
                    content = f"{content}\n\n[用户发送了一张图片]"
                else:
                    content = "[用户发送了一张图片]"

                # 记录图片处理结果，但不添加到用户消息中
                logger.info(f"Image processing result (not added to user message): {image_content[:100]}...")
            except Exception as e:
                logger.error(f"Failed to process image: {e}")
                # 如果图片处理失败，使用统一的标记
                if content:
                    content = f"{content}\n\n[用户发送了一张图片]"
                else:
                    content = "[用户发送了一张图片]"

        # 检查是否包含题目编号（仅用于获取题目信息，不特殊对待）
        problem_id = self._extract_problem_id(content)
        problem_info = None

        # 如果包含题目编号，尝试抓取题目信息
        if problem_id:
            logger.info(f"Detected problem ID: {problem_id}, fetching problem information")
            problem_info = await self._fetch_problem_info(problem_id)

        # 检查是否是图片消息
        is_image_message = "[用户发送了一张图片]" in content

        # 将用户消息添加到记忆
        await self._add_to_memory(user_id, content, "user", cancellation_token)

        # 如果是图片消息，直接生成回复，不需要检索记忆
        if is_image_message:
            logger.info(f"Image message detected from user {user_id}, skipping memory retrieval")
            response = "我看到您发送了一张图片，但我无法直接查看图片内容。请您简要描述一下图片中的内容，比如这是一道什么题目，或者您想问什么问题，这样我才能更好地帮助您。"

            # 将回复添加到记忆
            await self._add_to_memory(user_id, response, "assistant", cancellation_token)

            return response

        # 构建多层次记忆查询策略
        # 1. 首先查询最近的对话记录，特别强调时间顺序和最新的对话
        short_term_query = f"""
        请提供最近的完整对话记录，特别是最近几轮的对话:
        1. 必须按时间顺序排列，最近的对话必须排在最前面
        2. 优先返回最近3-5轮的对话，无论内容是否相关
        3. 确保包含用户最近的问题和助手的回答
        4. 排除所有包含"[用户发送了一张图片]"或"图片内容"的消息

        当前问题: {content}
        """

        # 2. 然后查询与当前问题直接相关的记忆
        topic_query = f"""
        请提供与当前问题直接相关的记忆:
        1. 优先返回与"{content}"这个具体问题最相关的内容
        2. 包括用户之前可能问过的类似问题
        3. 包括助手之前对类似问题的回答
        4. 排除所有包含"[用户发送了一张图片]"或"图片内容"的消息

        当前问题: {content}
        """

        # 3. 最后查询相关概念的记忆
        concept_query = f"""
        请提供与当前问题中涉及的概念相关的记忆:
        1. 识别当前问题中的关键概念
        2. 返回与这些概念相关的之前解释或讨论
        3. 按相关性排序，最相关的排在前面
        4. 排除所有包含"[用户发送了一张图片]"或"图片内容"的消息

        当前问题: {content}
        """

        # 执行多层次记忆查询
        short_term_memories = await self.memory.query(
            query=short_term_query,
            user_id=user_id,
            cancellation_token=cancellation_token
        )

        topic_memories = await self.memory.query(
            query=topic_query,
            user_id=user_id,
            cancellation_token=cancellation_token
        )

        concept_memories = await self.memory.query(
            query=concept_query,
            user_id=user_id,
            cancellation_token=cancellation_token
        )

        # 合并记忆结果，重新设计记忆检索逻辑
        all_memories = []

        # 收集所有记忆并按时间戳排序
        all_retrieved_memories = []

        # 处理最近对话记忆（优先级最高）
        if hasattr(short_term_memories, 'results') and short_term_memories.results:
            all_retrieved_memories.extend(short_term_memories.results)
            logger.info(f"Retrieved {len(short_term_memories.results)} recent conversation memories for user {user_id}")

        # 处理主题相关记忆（去重）
        if hasattr(topic_memories, 'results') and topic_memories.results:
            for memory in topic_memories.results:
                # 检查是否已经在最近对话记忆中
                is_duplicate = False
                if hasattr(memory, 'metadata') and memory.metadata:
                    memory_id = memory.metadata.get('memory_id', '')
                    for existing_memory in all_retrieved_memories:
                        if hasattr(existing_memory, 'metadata') and existing_memory.metadata:
                            if existing_memory.metadata.get('memory_id', '') == memory_id:
                                is_duplicate = True
                                break

                if not is_duplicate:
                    all_retrieved_memories.append(memory)

            logger.info(f"Retrieved {len(topic_memories.results)} topic-related memories for user {user_id}")

        # 处理概念相关记忆（去重）
        if hasattr(concept_memories, 'results') and concept_memories.results:
            for memory in concept_memories.results:
                # 检查是否已经存在
                is_duplicate = False
                if hasattr(memory, 'metadata') and memory.metadata:
                    memory_id = memory.metadata.get('memory_id', '')
                    for existing_memory in all_retrieved_memories:
                        if hasattr(existing_memory, 'metadata') and existing_memory.metadata:
                            if existing_memory.metadata.get('memory_id', '') == memory_id:
                                is_duplicate = True
                                break

                if not is_duplicate:
                    all_retrieved_memories.append(memory)

            logger.info(f"Retrieved {len(concept_memories.results)} concept-related memories for user {user_id}")

        # 按时间戳排序所有记忆（最新的在前）
        all_retrieved_memories.sort(
            key=lambda m: m.metadata.get('created_at', 0) if hasattr(m, 'metadata') and m.metadata else 0,
            reverse=True
        )

        # 重新组织记忆，确保对话配对（兼容新旧格式）
        conversation_pairs = {}
        user_memories = []
        assistant_memories = []
        other_memories = []

        for memory in all_retrieved_memories:
            if not hasattr(memory, 'metadata') or not memory.metadata:
                other_memories.append(memory)
                continue

            role = memory.metadata.get('role', '')
            # 兼容新旧格式的配对ID
            conversation_pair_id = memory.metadata.get('conversation_pair_id', '')
            if not conversation_pair_id:
                # 如果没有新格式的conversation_pair_id，尝试使用旧格式的message_pair_id
                conversation_pair_id = memory.metadata.get('message_pair_id', '')

            if role == "user":
                user_memories.append(memory)
                if conversation_pair_id:
                    if conversation_pair_id not in conversation_pairs:
                        conversation_pairs[conversation_pair_id] = {}
                    conversation_pairs[conversation_pair_id]['user'] = memory
            elif role == "assistant":
                assistant_memories.append(memory)
                if conversation_pair_id:
                    if conversation_pair_id not in conversation_pairs:
                        conversation_pairs[conversation_pair_id] = {}
                    conversation_pairs[conversation_pair_id]['assistant'] = memory
            else:
                other_memories.append(memory)

        logger.info(f"Organized memories - User: {len(user_memories)}, Assistant: {len(assistant_memories)}, Pairs: {len(conversation_pairs)}")

        # 构建平衡的对话历史
        # 优先使用完整的对话对，然后补充单独的消息
        balanced_memories = []

        # 首先添加完整的对话对（按时间顺序）
        sorted_pairs = sorted(
            conversation_pairs.items(),
            key=lambda x: x[1].get('user', x[1].get('assistant', type('obj', (), {'metadata': type('obj', (), {'created_at': 0})()})())).metadata.get('created_at', 0),
            reverse=True
        )

        for pair_id, pair_data in sorted_pairs[:10]:  # 最多取10对对话
            if 'user' in pair_data and 'assistant' in pair_data:
                # 完整对话对：先用户消息，后助手消息
                balanced_memories.append(pair_data['user'])
                balanced_memories.append(pair_data['assistant'])
            elif 'user' in pair_data:
                # 只有用户消息
                balanced_memories.append(pair_data['user'])
            elif 'assistant' in pair_data:
                # 只有助手消息
                balanced_memories.append(pair_data['assistant'])

        # 如果对话对不够，补充其他记忆
        if len(balanced_memories) < 20:  # 最多20条记忆
            # 添加未配对的用户消息
            unpaired_user_memories = [m for m in user_memories if not any(
                m.metadata.get('conversation_pair_id', '') == pair_id for pair_id in conversation_pairs.keys()
            )]

            # 添加未配对的助手消息
            unpaired_assistant_memories = [m for m in assistant_memories if not any(
                m.metadata.get('conversation_pair_id', '') == pair_id for pair_id in conversation_pairs.keys()
            )]

            # 按时间排序并添加
            unpaired_memories = unpaired_user_memories + unpaired_assistant_memories
            unpaired_memories.sort(
                key=lambda m: m.metadata.get('created_at', 0) if hasattr(m, 'metadata') and m.metadata else 0,
                reverse=True
            )

            remaining_slots = 20 - len(balanced_memories)
            balanced_memories.extend(unpaired_memories[:remaining_slots])

        # 最终按时间排序（最新的在前）
        balanced_memories.sort(
            key=lambda m: m.metadata.get('created_at', 0) if hasattr(m, 'metadata') and m.metadata else 0,
            reverse=True
        )

        # 创建合并后的记忆结果对象
        memory_results = type('MemoryResults', (), {'results': all_memories})

        # 记录检索到的记忆数量和内容摘要
        if all_memories:
            memory_count = len(all_memories)

            # 按时间顺序显示最近的对话历史（最近的在前）
            logger.info(f"Retrieved total {memory_count} memories for user {user_id}")
            logger.info("最近的对话历史（按时间顺序，最近的在前）：")

            for i, memory in enumerate(all_memories[:6], 1):  # 显示最近的6条记忆
                if hasattr(memory, 'content') and hasattr(memory, 'metadata') and memory.metadata:
                    role = memory.metadata.get('role', 'unknown')
                    content = str(memory.content).replace(f'[{role}] ', '')[:100]


                    if role == "user":
                        logger.info(f"{i}. 用户: {content}")
                    elif role == "assistant":
                        logger.info(f"{i}. 助手: {content}")
                    else:
                        logger.info(f"{i}. {role}: {content}")

            # 显示对话配对统计
            complete_pairs = sum(1 for pair_data in conversation_pairs.values()
                               if 'user' in pair_data and 'assistant' in pair_data)
            incomplete_pairs = len(conversation_pairs) - complete_pairs

            logger.info(f"对话配对统计: 完整对话对 {complete_pairs}, 不完整对话对 {incomplete_pairs}")
        else:
            logger.info(f"No memories retrieved for user {user_id}")

        # 生成回复
        response = await self._generate_response(user_id, content, problem_info, memory_results, cancellation_token)

        # 将回复添加到记忆
        await self._add_to_memory(user_id, response, "assistant", cancellation_token)

        return response

    async def _process_image(self, image_data: bytes, image_type: str) -> str:
        """处理图片数据，提取图片中的文本或其他内容

        Args:
            image_data: 图片二进制数据
            image_type: 图片MIME类型

        Returns:
            str: 从图片中提取的文本内容
        """
        try:
            # 这里我们使用一个简单的方法来解析图片内容
            # 在实际应用中，你可能需要使用OCR服务或其他图像处理服务

            # 由于当前版本的autogen_core可能不直接支持图片处理，我们使用一个替代方案
            # 将图片保存到临时文件并使用其他工具处理，或者使用外部API

            import tempfile
            import os

            # 创建临时文件保存图片
            with tempfile.NamedTemporaryFile(delete=False, suffix=f".{image_type.split('/')[-1] if '/' in image_type else 'jpg'}") as temp_file:
                temp_file.write(image_data)
                temp_file_path = temp_file.name

            try:
                # 这里可以调用OCR服务或其他图像分析服务
                # 例如，可以使用Google Cloud Vision API、Azure Computer Vision等
                # 为了简单起见，我们这里返回一个基本描述

                # 提供更友好的回复，告知用户我们已收到图片但需要他们提供更多信息
                ocr_result = "我看到您发送了一张图片。由于目前我无法直接分析图片内容，请您简要描述一下图片中的内容，比如这是一道什么题目，或者您想问什么问题，这样我才能更好地帮助您。"

                # 清理临时文件
                os.unlink(temp_file_path)

                return ocr_result
            except Exception as e:
                # 清理临时文件
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
                raise e

        except Exception as e:
            logger.error(f"Error processing image: {str(e)}")
            # 提供更友好的错误消息，不暴露技术细节
            return "我在处理您发送的图片时遇到了一些问题。请您描述一下图片中的内容，或者直接输入您的问题，这样我就能更好地帮助您了。"

    def _extract_problem_id(self, content: str) -> Optional[str]:
        """从用户消息中提取题目编号

        Args:
            content: 用户消息内容

        Returns:
            Optional[str]: 题目编号，如果未找到则返回None
        """
        # 匹配洛谷题目编号格式（如P1000, P1005, P1560）
        pattern = r'P\d{4,}'
        match = re.search(pattern, content)
        if match:
            return match.group(0)  # 使用group(0)返回整个匹配
        return None

    def _extract_problem_id_from_memory(self, memory_results: Any) -> Optional[str]:
        """从记忆结果中提取题目编号

        Args:
            memory_results: 记忆查询结果

        Returns:
            Optional[str]: 题目编号，如果未找到则返回None
        """
        if not memory_results or not hasattr(memory_results, 'results') or not memory_results.results:
            return None

        # 遍历记忆结果，寻找包含题目编号的记忆
        for memory in memory_results.results:
            if hasattr(memory, 'content'):
                content = str(memory.content)
                problem_id = self._extract_problem_id(content)
                if problem_id:
                    return problem_id

        return None

    async def _extract_recent_problem_info(self, user_id: str, memory_results: Any) -> Optional[Dict[str, Any]]:
        """从记忆中提取最近讨论的题目信息

        Args:
            user_id: 用户ID
            memory_results: 记忆查询结果

        Returns:
            Optional[Dict[str, Any]]: 题目信息字典，如果未找到则返回None
        """
        try:
            # 首先尝试从当前的记忆结果中提取题目信息
            if memory_results and hasattr(memory_results, 'results') and memory_results.results:
                # 遍历记忆结果，寻找包含题目信息的记忆
                for memory in memory_results.results:
                    if hasattr(memory, 'content'):
                        content = str(memory.content)
                        # 检查是否包含题目编号
                        problem_id = self._extract_problem_id(content)
                        if problem_id:
                            logger.info(f"Found problem ID {problem_id} in memory for user {user_id}")

                            # 检查是否包含题目详细信息（如标题、描述等）
                            if "标题" in content or "title" in content.lower() or "描述" in content or "description" in content.lower():
                                # 尝试构建题目信息字典
                                problem_info = {
                                    "problem_id": problem_id
                                }

                                # 提取标题
                                title_match = re.search(r'标题[：:]\s*(.+?)(?:\n|$)', content)
                                if title_match:
                                    problem_info["title"] = title_match.group(1).strip()

                                # 提取描述
                                desc_match = re.search(r'描述[：:]\s*(.+?)(?:\n|$)', content)
                                if desc_match:
                                    problem_info["description"] = desc_match.group(1).strip()

                                # 如果找到了足够的信息，返回题目信息字典
                                if len(problem_info) > 1:  # 不仅仅只有problem_id
                                    return problem_info

            # 如果当前记忆结果中没有找到足够的信息，尝试专门查询题目信息
            # 首先查询最近讨论的题目编号
            memory_query = "最近讨论的编程题目是什么"
            recent_memories = await self.memory.query(
                query=memory_query,
                user_id=user_id,
                cancellation_token=None
            )

            # 从记忆中提取题目编号
            problem_id = self._extract_problem_id_from_memory(recent_memories)
            if not problem_id:
                return None

            # 查询与该题目相关的详细信息
            problem_query = f"题目 {problem_id} 的详细信息"
            problem_memories = await self.memory.query(
                query=problem_query,
                user_id=user_id,
                cancellation_token=None
            )

            if not problem_memories or not hasattr(problem_memories, 'results') or not problem_memories.results:
                # 如果没有找到详细信息，但至少有题目编号，返回基本信息
                return {"problem_id": problem_id}

            # 构建题目信息字典
            problem_info = {"problem_id": problem_id}

            # 遍历记忆结果，提取题目详细信息
            for memory in problem_memories.results:
                if hasattr(memory, 'content'):
                    content = str(memory.content)

                    # 提取标题
                    title_match = re.search(r'标题[：:]\s*(.+?)(?:\n|$)', content)
                    if title_match and "title" not in problem_info:
                        problem_info["title"] = title_match.group(1).strip()

                    # 提取描述
                    desc_match = re.search(r'描述[：:]\s*(.+?)(?:\n|$)', content)
                    if desc_match and "description" not in problem_info:
                        problem_info["description"] = desc_match.group(1).strip()

                    # 提取输入格式
                    input_match = re.search(r'输入[格式]?[：:]\s*(.+?)(?:\n|$)', content)
                    if input_match and "input_format" not in problem_info:
                        problem_info["input_format"] = input_match.group(1).strip()

                    # 提取输出格式
                    output_match = re.search(r'输出[格式]?[：:]\s*(.+?)(?:\n|$)', content)
                    if output_match and "output_format" not in problem_info:
                        problem_info["output_format"] = output_match.group(1).strip()

            return problem_info

        except Exception as e:
            logger.error(f"Error extracting recent problem info: {str(e)}")
            return None

    async def _retrieve_problem_info(self, problem_id: Optional[str] = None, user_id: Optional[str] = None) -> str:
        """检索特定题目的详细信息

        Args:
            problem_id: 题目编号，如果为None则尝试从记忆中找到最近讨论的题目
            user_id: 用户ID，如果为None则使用当前用户ID

        Returns:
            str: 题目信息的文本描述
        """
        # 获取当前用户ID
        if not user_id:
            # 从上下文中获取用户ID，这里假设在handle_user_message中设置了当前用户ID
            user_id = getattr(self, 'current_user_id', None)
            if not user_id:
                return "错误：未提供用户ID，无法检索记忆"

        # 如果未提供题目编号，尝试从记忆中找到最近讨论的题目
        if not problem_id:
            # 查询用户的记忆，寻找最近讨论的题目
            memory_query = "最近讨论的编程题目编号是什么"
            recent_memories = await self.memory.query(
                query=memory_query,
                user_id=user_id,
                cancellation_token=None
            )
            logger.info(f"Searching for problem ID in memory for user {user_id}")

            # 从记忆中提取题目编号
            problem_id = self._extract_problem_id_from_memory(recent_memories)
            if not problem_id:
                return "未找到最近讨论的题目编号，请提供具体的题目编号"

        # 查询用户的记忆，寻找与该题目相关的信息
        memory_query = f"题目 {problem_id} 的详细信息"
        problem_memories = await self.memory.query(memory_query, user_id)

        if not problem_memories or not hasattr(problem_memories, 'results') or not problem_memories.results:
            return f"抱歉，我没有关于题目 {problem_id} 的详细信息。请提供题目描述，我会尽力帮助解答。"

        # 构建题目信息的文本描述
        problem_info = f"题目 {problem_id}：\n\n"

        # 遍历记忆结果，提取题目信息
        for memory in problem_memories.results:
            if hasattr(memory, 'content'):
                content = str(memory.content)
                if problem_id in content:
                    problem_info += f"{content}\n\n"

        return problem_info

    async def _retrieve_user_history(self, user_id: Optional[str] = None, query: str = "最近的对话记录") -> str:
        """检索用户的历史对话记录

        Args:
            user_id: 用户ID，如果为None则使用当前用户ID
            query: 查询内容，默认为"最近的对话记录"

        Returns:
            str: 用户历史对话记录的文本描述
        """
        # 获取当前用户ID
        if not user_id:
            # 从上下文中获取用户ID，这里假设在handle_user_message中设置了当前用户ID
            user_id = getattr(self, 'current_user_id', None)
            if not user_id:
                return "错误：未提供用户ID，无法检索记忆"

        # 查询用户的记忆
        user_memories = await self.memory.query(
            query=query,
            user_id=user_id,
            cancellation_token=None
        )
        logger.info(f"Retrieved user history for user {user_id}")

        if not user_memories or not hasattr(user_memories, 'results') or not user_memories.results:
            return f"未找到用户 {user_id} 的历史对话记录"

        # 构建用户历史对话记录的文本描述
        history = f"用户 {user_id} 的历史对话记录：\n\n"

        # 遍历记忆结果，提取对话记录
        for i, memory in enumerate(user_memories.results):
            if hasattr(memory, 'content'):
                content = str(memory.content)
                history += f"{i+1}. {content}\n\n"

        return history

    def _extract_problem_info_from_memories(self, memories: List[Any], problem_id: str) -> Optional[Dict[str, Any]]:
        """从记忆中提取题目信息

        Args:
            memories: 记忆列表
            problem_id: 题目编号

        Returns:
            Optional[Dict[str, Any]]: 题目信息字典，如果未找到则返回None
        """
        if not memories:
            return None

        # 初始化题目信息字典
        problem_info = {
            "problem_id": problem_id
        }

        # 遍历记忆，提取题目信息
        for memory in memories:
            if not hasattr(memory, 'content'):
                continue

            content = str(memory.content)

            # 检查是否包含题目信息
            if problem_id in content:
                # 提取标题
                title_match = re.search(r'(?:标题|任务名称|题目)[：:]\s*(.+?)(?:\n|$)', content)
                if title_match and "title" not in problem_info:
                    problem_info["title"] = title_match.group(1).strip()

                # 提取描述
                desc_match = re.search(r'(?:描述|冒险描述)[：:]\s*(.+?)(?:\n|$)', content)
                if desc_match and "description" not in problem_info:
                    problem_info["description"] = desc_match.group(1).strip()

                # 提取输入格式
                input_match = re.search(r'(?:输入格式|魔法输入)[：:]\s*(.+?)(?:\n|$)', content)
                if input_match and "input_format" not in problem_info:
                    problem_info["input_format"] = input_match.group(1).strip()

                # 提取输出格式
                output_match = re.search(r'(?:输出格式|期待结果)[：:]\s*(.+?)(?:\n|$)', content)
                if output_match and "output_format" not in problem_info:
                    problem_info["output_format"] = output_match.group(1).strip()

                # 提取示例（简化处理）
                if "examples" not in problem_info and ("示例" in content or "样例" in content):
                    problem_info["examples"] = []

                    # 简单示例提取
                    input_example = re.search(r'(?:输入|输入宝藏)[：:]\s*(.+?)(?:\n|$)', content)
                    output_example = re.search(r'(?:输出|输出宝藏)[：:]\s*(.+?)(?:\n|$)', content)

                    if input_example and output_example:
                        problem_info["examples"].append({
                            "input_example": input_example.group(1).strip(),
                            "output_example": output_example.group(1).strip()
                        })

        # 如果只有题目ID，没有其他信息，返回None
        if len(problem_info) <= 1:
            return None

        return problem_info

    async def _fetch_problem_info(self, problem_id: str) -> Optional[Dict[str, Any]]:
        """抓取题目信息

        Args:
            problem_id: 题目编号

        Returns:
            Optional[Dict[str, Any]]: 题目信息，如果抓取失败则返回None
        """
        try:
            # 构建洛谷题目URL
            url = f"https://www.luogu.com.cn/problem/{problem_id}"
            logger.info(f"Fetching problem information from {url}")

            # 调用爬虫工程师代理抓取题目信息
            result = await self.crawler_agent._crawl_and_parsing_result(
                target_url=url,
                content_type=ContentType.COMPETITION_TOPIC,
                parse_type=ParseType.ITEM_DETAIL
            )

            if not result:
                logger.warning(f"Failed to fetch problem information for {problem_id}")
                return None

            logger.info(f"Successfully fetched problem information for {problem_id}")
            return result
        except Exception as e:
            logger.error(f"Error fetching problem information for {problem_id}: {str(e)}")
            return None

    async def _add_to_memory(self, user_id: str, content: str, role: str, cancellation_token: Optional[CancellationToken] = None) -> None:
        """将消息添加到用户记忆

        Args:
            user_id: 用户ID
            content: 消息内容
            role: 消息角色（"user"或"assistant"）
            cancellation_token: 取消令牌
        """
        try:
            # 使用时间戳作为唯一标识符，确保消息顺序
            current_timestamp = time.time()

            # 为了确保消息配对，我们使用会话级别的计数器
            # 但是要确保用户和助手消息能够正确配对
            if user_id not in self._user_message_counts:
                # 如果是新用户，尝试从记忆中获取最新的消息计数
                try:
                    count_query = "最近的消息计数"
                    count_results = await self.memory.query(
                        query=count_query,
                        user_id=user_id,
                        cancellation_token=cancellation_token
                    )

                    # 从查询结果中提取最高消息计数
                    highest_count = 0
                    if hasattr(count_results, 'results') and count_results.results:
                        for memory in count_results.results:
                            if hasattr(memory, 'metadata') and memory.metadata:
                                msg_count = memory.metadata.get('message_count', 0)
                                if isinstance(msg_count, (int, float)) and msg_count > highest_count:
                                    highest_count = int(msg_count)

                    self._user_message_counts[user_id] = highest_count
                    logger.info(f"Initialized message count for user {user_id}: {highest_count}")
                except Exception as e:
                    logger.error(f"Error retrieving initial message count for user {user_id}: {e}")
                    self._user_message_counts[user_id] = 0

            # 为每个消息分配唯一的序列号
            # 用户消息和助手消息使用不同的计数策略确保配对
            if role == "user":
                # 用户消息：递增计数
                self._user_message_counts[user_id] += 1
                current_message_count = self._user_message_counts[user_id]
                # 使用旧格式的message_pair_id以保持兼容性
                message_pair_id = f"{user_id}_{current_message_count}"
            else:
                # 助手消息：使用相同的计数，确保与用户消息配对
                current_message_count = self._user_message_counts[user_id]
                # 使用旧格式的message_pair_id以保持兼容性
                message_pair_id = f"{user_id}_{current_message_count}"

            logger.info(f"User {user_id} message count: {current_message_count} (role: {role})")

            # 构建记忆元数据，包含更详细的时间和顺序信息
            metadata = {
                "user_id": user_id,
                "role": role,
                "timestamp": datetime.now().isoformat(),
                "created_at": current_timestamp,
                "memory_level": "short_term",
                "message_count": current_message_count,
                "message_pair_id": message_pair_id,  # 使用旧格式以保持兼容性
                "sequence_number": int(current_timestamp * 1000000),  # 微秒级时间戳确保唯一性
                "message_type": role  # 明确标识消息类型
            }

            # 创建记忆内容 - 使用统一的格式
            formatted_content = f"[{role}] {content}"

            memory_content = MemoryContent(
                content=formatted_content,
                mime_type=MemoryMimeType.TEXT,
                metadata=metadata
            )

            # 添加到记忆系统
            await self.memory.add(
                content=memory_content,
                user_id=user_id,
                cancellation_token=cancellation_token
            )
            logger.info(f"Added {role} message to memory for user {user_id} with count {current_message_count}")

            # 检查是否需要触发基于消息数量的记忆压缩
            await self._check_and_trigger_compression(user_id, current_message_count, cancellation_token)

        except Exception as e:
            logger.error(f"Error adding message to memory: {str(e)}", exc_info=True)

    async def _check_and_trigger_compression(self, user_id: str, message_count: int, cancellation_token: Optional[CancellationToken] = None) -> None:
        """检查是否需要触发记忆压缩，并在需要时触发

        Args:
            user_id: 用户ID
            message_count: 当前消息计数
            cancellation_token: 取消令牌
        """
        try:
            # 获取配置
            config = get_config()

            try:
                # 使用直接索引方式获取配置
                short_to_medium_count = config['agents']['memory_system']['compression_thresholds']['short_to_medium_count']
                medium_to_long_count = config['agents']['memory_system']['compression_thresholds']['medium_to_long_count']
            except KeyError as e:
                # 如果配置中没有相应的键，使用默认值
                logger.warning(f"Configuration key not found in compression check: {e}, using default values")
                short_to_medium_count = 20  # 默认20条消息
                medium_to_long_count = 50   # 默认50条消息

            # 检查是否需要触发压缩
            if message_count >= medium_to_long_count and hasattr(self.memory, 'compress_memories'):
                # 触发中期到长期的压缩
                logger.info(f"Triggering medium to long term compression for user {user_id} (message count: {message_count})")
                await self.memory.compress_memories(
                    user_id=user_id,
                    from_level=MemoryLevel.MEDIUM_TERM,
                    to_level=MemoryLevel.LONG_TERM,
                    cancellation_token=cancellation_token
                )
            elif message_count >= short_to_medium_count and hasattr(self.memory, 'compress_memories'):
                # 触发短期到中期的压缩
                logger.info(f"Triggering short to medium term compression for user {user_id} (message count: {message_count})")
                await self.memory.compress_memories(
                    user_id=user_id,
                    from_level=MemoryLevel.SHORT_TERM,
                    to_level=MemoryLevel.MEDIUM_TERM,
                    cancellation_token=cancellation_token
                )
        except Exception as e:
            logger.error(f"Error checking and triggering compression: {str(e)}")
            # 继续执行，不要因为压缩失败而中断操作

    async def _generate_response(
        self,
        user_id: str,
        user_message: str,
        problem_info: Optional[Dict[str, Any]],
        memory_results: Any,
        cancellation_token: Optional[CancellationToken] = None
    ) -> str:
        """生成回复

        Args:
            user_id: 用户ID
            user_message: 用户消息
            problem_info: 题目信息（如果有）
            memory_results: 记忆查询结果
            cancellation_token: 取消令牌

        Returns:
            str: 生成的回复
        """
        try:
            # 检查是否是图片消息，如果是，直接返回固定回复，不考虑记忆或上下文
            if "[用户发送了一张图片]" in user_message:
                logger.info(f"Detected image message from user {user_id}, returning standard image response")
                # 清空记忆结果，避免受到之前对话的影响
                if memory_results and hasattr(memory_results, 'results'):
                    # 过滤掉所有包含图片相关内容的记忆
                    filtered_memories = []
                    for memory in memory_results.results:
                        if hasattr(memory, 'content'):
                            content = str(memory.content)
                            if "[用户发送了一张图片]" not in content and "[图片内容]" not in content and "图片" not in content:
                                filtered_memories.append(memory)

                    # 更新记忆结果
                    memory_results.results = filtered_memories
                    logger.info(f"Filtered memory results for image message, remaining: {len(filtered_memories)}")

                # 无论如何，都返回标准图片响应
                return "我看到您发送了一张图片，但我无法直接查看图片内容。请您简要描述一下图片中的内容，比如这是一道什么题目，或者您想问什么问题，这样我才能更好地帮助您。"

            # 构建提示
            prompt = self._build_response_prompt(user_id, user_message, problem_info, memory_results)

            # 获取系统消息（如果没有，则使用默认值）
            system_message = getattr(self, 'system_message', """你是一位C++编程教师，负责回答学生的编程问题。

你的工作原则：
1. 简洁明了地回答问题，不做无关扩展
2. 确保每条回复不超过2048字节（约600-700中文字）
3. 使用正常、专业的语气，避免过度使用emoji或儿童化语言
4. 解释概念时使用清晰的比喻，但保持专业性
5. 提供简单易懂的代码示例，避免过于复杂的实现

回答格式要求：
1. 使用纯文本格式回复，不要使用Markdown格式
2. 不要使用```代码块标记，直接使用缩进和空行来展示代码
3. 不要使用粗体、斜体等Markdown格式，使用普通文本
4. 使用数字和字母编号来组织内容，如"1."、"2."、"a."、"b."等
5. 使用空行分隔段落，使文本易于阅读

回答编程问题时：
- 直接切入主题，简明扼要地解释核心概念
- 使用简单的类比帮助理解，但不过度简化
- 提供符合题目要求的伪代码或简单代码示例（使用缩进而非代码块标记）
- 分步骤解释时保持简洁
- 如果问题复杂，优先解释最关键的部分

当学生提到特定题目编号（如P1005）时，你会获取题目信息，并用清晰的语言解释题目要求。

记住：学生需要专业、清晰的指导，而非过度简化的解释。学生通过微信查看你的回复，微信不支持Markdown格式，所以必须使用纯文本格式。""")

            # 检查model_client是否存在
            if not hasattr(self, 'model_client') or self.model_client is None:
                logger.error("model_client not initialized")
                return "抱歉，系统暂时无法回应。请稍后再试或联系管理员。"

            # 调用LLM生成回复，使用正确的消息格式
            messages = [
                SystemMessage(content=system_message),
                UserMessage(content=prompt, source="user")
            ]

            # 使用正确的方法调用模型
            try:
                # 首选方法是 create
                if hasattr(self.model_client, 'create'):
                    logger.info("Using model_client.create method")
                    response = await self.model_client.create(messages=messages, cancellation_token=cancellation_token)
                # 备选方法是 complete
                elif hasattr(self.model_client, 'complete'):
                    logger.info("Using model_client.complete method")
                    response = await self.model_client.complete(messages=messages, cancellation_token=cancellation_token)
                # 再次备选方法是 chat_completion
                elif hasattr(self.model_client, 'chat_completion'):
                    logger.info("Using model_client.chat_completion method")
                    response = await self.model_client.chat_completion(messages=messages, cancellation_token=cancellation_token)
                # 最后尝试直接调用
                else:
                    logger.info("Trying to call model_client directly")
                    response = await self.model_client(messages=messages, cancellation_token=cancellation_token)

                logger.info(f"Model response type: {type(response)}")
                logger.info(f"Model response attributes: {dir(response) if response else 'None'}")
            except Exception as e:
                logger.error(f"Failed to call model_client: {e}")
                raise ValueError(f"Failed to generate response: {e}")

            if not response or not response.content:
                logger.warning(f"Empty response from LLM for user {user_id}")
                return "抱歉，未能生成有效回复。请重新描述您的问题，我会尽力提供帮助。"

            # 获取响应内容
            response_text = response.content

            # 检查响应长度是否超过2048字节
            response_bytes = len(response_text.encode('utf-8'))
            logger.info(f"Response length: {response_bytes} bytes")

            # 如果超过限制，请求更简洁的回复
            if response_bytes > 2048:
                logger.warning(f"Response exceeds 2048 bytes limit: {response_bytes} bytes")

                # 添加提示要求更简洁的回复
                messages.append(UserMessage(
                    content="你的回复超过了2048字节限制，请提供更简洁的版本，只包含最核心的内容，去除所有不必要的解释和修饰语。",
                    source="user"
                ))

                # 重新生成更简洁的回复
                try:
                    if hasattr(self.model_client, 'create'):
                        simplified_response = await self.model_client.create(messages=messages, cancellation_token=cancellation_token)
                    elif hasattr(self.model_client, 'complete'):
                        simplified_response = await self.model_client.complete(messages=messages, cancellation_token=cancellation_token)
                    elif hasattr(self.model_client, 'chat_completion'):
                        simplified_response = await self.model_client.chat_completion(messages=messages, cancellation_token=cancellation_token)
                    else:
                        simplified_response = await self.model_client(messages=messages, cancellation_token=cancellation_token)

                    response_text = simplified_response.content

                    # 再次检查长度
                    response_bytes = len(response_text.encode('utf-8'))
                    logger.info(f"Simplified response length: {response_bytes} bytes")

                    # 如果仍然超过限制，强制截断
                    if response_bytes > 2048:
                        logger.warning(f"Response still exceeds limit after simplification: {response_bytes} bytes")
                        # 强制截断到安全长度
                        while len(response_text.encode('utf-8')) > 2000:  # 留出余量
                            response_text = response_text[:int(len(response_text) * 0.9)]  # 每次减少10%

                        # 添加截断提示
                        response_text += "\n\n(回复因长度限制被截断)"

                        # 最终长度
                        logger.info(f"Truncated response length: {len(response_text.encode('utf-8'))} bytes")
                except Exception as e:
                    logger.error(f"Error generating simplified response: {e}")
                    # 如果简化失败，直接截断原始响应
                    while len(response_text.encode('utf-8')) > 2000:
                        response_text = response_text[:int(len(response_text) * 0.9)]
                    response_text += "\n\n(回复因长度限制被截断)"

            # 处理响应文本，移除Markdown格式，优化为微信友好的纯文本格式
            response_text = self._format_response_for_wechat(response_text)

            return response_text
        except Exception as e:
            logger.error(f"Error generating response: {str(e)}")
            return "抱歉，处理您的请求时出现了问题。请稍后再试或重新提问。"

    def _format_response_for_wechat(self, text: str) -> str:
        """
        将响应文本格式化为微信友好的纯文本格式

        Args:
            text: 原始响应文本

        Returns:
            str: 格式化后的文本
        """
        try:
            # 移除Markdown代码块标记，保留代码内容并添加适当的缩进
            lines = text.split('\n')
            formatted_lines = []
            in_code_block = False
            code_indent = '    '  # 代码块的缩进

            for line in lines:
                # 处理代码块开始和结束标记
                if line.strip().startswith('```'):
                    if in_code_block:
                        # 代码块结束
                        in_code_block = False
                        formatted_lines.append('')  # 添加空行分隔
                    else:
                        # 代码块开始
                        in_code_block = True
                        # 如果代码块标记包含语言信息，添加注释
                        lang = line.strip()[3:].strip()
                        if lang:
                            formatted_lines.append(f"// {lang} 代码:")
                            formatted_lines.append('')  # 添加空行
                    continue

                # 处理代码块内的内容
                if in_code_block:
                    formatted_lines.append(code_indent + line)
                else:
                    # 处理普通文本
                    # 移除Markdown的粗体和斜体标记
                    # line = line.replace('**', '').replace('__', '').replace('*', '').replace('_', '')

                    # 移除Markdown的链接格式，只保留链接文本
                    line = re.sub(r'\[([^\]]+)\]\([^)]+\)', r'\1', line)

                    formatted_lines.append(line)

            # 确保代码块后有空行
            result = '\n'.join(formatted_lines)

            # 移除连续的多个空行，最多保留两个连续空行
            result = re.sub(r'\n{3,}', '\n\n', result)

            # 确保以单个换行符结束
            result = result.rstrip() + '\n'

            return result
        except Exception as e:
            logger.error(f"Error formatting response for WeChat: {str(e)}")
            # 如果格式化失败，返回原始文本
            return text

    def _build_response_prompt(self, user_id: str, user_message: str, problem_info: Optional[Dict[str, Any]], memory_results: Any) -> str:
        """构建回复提示

        Args:
            user_id: 用户ID（用于记录日志和未来扩展）
            user_message: 用户消息
            problem_info: 题目信息（如果有）
            memory_results: 记忆查询结果

        Returns:
            str: 构建的提示
        """
        # 记录日志，使用user_id参数
        logger.debug(f"Building response prompt for user {user_id}")

        # 检查是否是图片消息 - 注意：现在在_generate_response中已经直接处理了图片消息
        # 这里保留这段代码是为了防止有些情况下图片消息没有被_generate_response捕获
        if "[用户发送了一张图片]" in user_message:
            prompt = "学生发送了一张图片，但我无法直接查看图片内容。请引导学生描述图片内容，以便我能更好地帮助他们。\n\n"
            prompt += "请回复：'我看到您发送了一张图片，但我无法直接查看图片内容。请您简要描述一下图片中的内容，比如这是一道什么题目，或者您想问什么问题，这样我才能更好地帮助您。'\n\n"
            # 直接返回提示，不需要添加其他内容
            return prompt
        else:
            prompt = f"学生的C++问题：{user_message}\n\n"

        # 添加题目信息（如果有）
        if problem_info:
            prompt += "题目信息：\n"
            if isinstance(problem_info, dict):
                # 格式化题目信息
                if "title" in problem_info:
                    prompt += f"标题：{problem_info.get('title', '')}\n"
                if "problem_id" in problem_info:
                    prompt += f"题号：{problem_info.get('problem_id', '')}\n"
                if "description" in problem_info:
                    prompt += f"描述：{problem_info.get('description', '')}\n"
                if "input_format" in problem_info:
                    prompt += f"输入格式：{problem_info.get('input_format', '')}\n"
                if "output_format" in problem_info:
                    prompt += f"输出格式：{problem_info.get('output_format', '')}\n"

                # 添加示例（限制数量以控制长度）
                examples = problem_info.get("examples", [])
                if examples and isinstance(examples, list):
                    prompt += "示例：\n"
                    for i, example in enumerate(examples[:2]):  # 最多添加2条示例
                        if isinstance(example, dict):
                            prompt += f"示例 {i+1}：\n"
                            prompt += f"输入：{example.get('input_example', '')}\n"
                            prompt += f"输出：{example.get('output_example', '')}\n"
                            if example.get('explanation'):
                                # 限制解释长度
                                explanation = example.get('explanation', '')
                                if len(explanation) > 100:
                                    explanation = explanation[:100] + "..."
                                prompt += f"说明：{explanation}\n"
            else:
                # 如果不是字典格式，直接添加原始内容（限制长度）
                content = str(problem_info)
                if len(content) > 300:
                    content = content[:300] + "..."
                prompt += content

            prompt += "\n"

        # 处理记忆信息 - 按照记忆类型组织
        if memory_results and hasattr(memory_results, 'results') and memory_results.results:
            # 短期记忆（最近的对话）
            short_term_memories = []
            # 主题相关记忆
            topic_related_memories = []
            # 概念相关记忆
            concept_related_memories = []
            # 最近对话记忆
            recent_conversation_memories = []

            # 遍历记忆结果，按类型分类
            for memory in memory_results.results:
                if not hasattr(memory, 'content'):
                    continue

                content = str(memory.content)

                # --- Start: Revised logic for populating memory lists ---

                # 1. Populate recent_conversation_memories (list of MemoryObjects for dialogue history)
                #    This list should contain all user and assistant turns from memory_results.
                is_dialogue_turn = False
                memory_role = None
                if hasattr(memory, 'metadata') and memory.metadata:
                    memory_role = memory.metadata.get('role')

                if memory_role == 'user' or memory_role == 'assistant':
                    is_dialogue_turn = True
                elif "[user]" in content.lower() or "[assistant]" in content.lower(): # Fallback content sniffing
                    is_dialogue_turn = True

                if is_dialogue_turn:
                    if memory not in recent_conversation_memories: # Avoid duplicates
                        recent_conversation_memories.append(memory)

                # 2. Populate other memory lists (short_term_memories (strings), topic_related_memories, concept_related_memories)
                #    based on memory_level or content features, for other parts of the prompt.
                memory_level_from_meta = None
                if hasattr(memory, 'metadata') and memory.metadata:
                    memory_level_from_meta = memory.metadata.get('memory_level')

                if memory_level_from_meta == "short_term":
                    if content not in short_term_memories: # short_term_memories is a list of strings
                        short_term_memories.append(content)
                    # recent_conversation_memories already handled above if it was a dialogue turn
                elif memory_level_from_meta == "medium_term":
                    if memory not in concept_related_memories:
                        concept_related_memories.append(memory)
                elif memory_level_from_meta == "long_term":
                    if memory not in topic_related_memories:
                        topic_related_memories.append(memory)
                else: # Fallback logic if memory_level is not explicitly set
                    if "[user]" in content or "[assistant]" in content:
                        # This part is mainly for populating short_term_memories (list of strings)
                        # if not already added by memory_level == "short_term"
                        # The complex related_exists logic for short_term_memories (strings) is preserved here.
                        is_user_msg_fallback = "[user]" in content
                        is_assistant_msg_fallback = "[assistant]" in content
                        msg_content_fallback = content.split("]", 1)[1].strip() if "]" in content else content
                        msg_keywords_fallback = ' '.join(msg_content_fallback.split()[:5])

                        related_exists_in_short_term_strings = False
                        for existing_short_term_str in short_term_memories:
                            if is_user_msg_fallback and "[assistant]" in existing_short_term_str and msg_keywords_fallback in existing_short_term_str:
                                related_exists_in_short_term_strings = True; break
                            if is_assistant_msg_fallback and "[user]" in existing_short_term_str and msg_keywords_fallback in existing_short_term_str:
                                related_exists_in_short_term_strings = True; break

                        if not related_exists_in_short_term_strings or len(short_term_memories) < 10:
                            if content not in short_term_memories:
                                short_term_memories.append(content)
                        # recent_conversation_memories (objects) was already handled by the is_dialogue_turn check

                    elif "```" in content or "代码" in content or "示例" in content:
                        if memory not in concept_related_memories:
                            concept_related_memories.append(memory)
                    elif "学习风格" in content or "偏好" in content or "理解水平" in content:
                        if memory not in topic_related_memories:
                            topic_related_memories.append(memory)
                    else: # Default for short_term_memories (strings) in fallback
                        if content not in short_term_memories:
                            short_term_memories.append(content)
                        # recent_conversation_memories (objects) was already handled by the is_dialogue_turn check
                # --- End: Revised logic for populating memory lists ---

            # 添加最近的对话历史（优先级最高）
            if recent_conversation_memories:
                prompt += "最近的对话历史（按时间顺序，最近的在前）：\n"

                # Sort recent_conversation_memories (list of MemoryObjects) directly by 'created_at'
                # Newest first (descending order of timestamp)
                recent_conversation_memories.sort(
                    key=lambda mem_obj: mem_obj.metadata.get('created_at', 0.0) if hasattr(mem_obj, 'metadata') and mem_obj.metadata else 0.0,
                    reverse=True
                )

                # Iterate through the sorted MemoryObjects and add them to the prompt
                # Limit to a certain number of recent turns, e.g., last 5-7 turns (user or assistant)
                # For simplicity, let's take up to 7 most recent turns.
                turns_to_display = 0
                max_turns_for_history = 7 # Max number of individual messages (user or assistant)

                for i, mem_obj in enumerate(recent_conversation_memories):
                    if turns_to_display >= max_turns_for_history:
                        break

                    if hasattr(mem_obj, 'content') and hasattr(mem_obj, 'metadata') and mem_obj.metadata:
                        msg_content_str = str(mem_obj.content)
                        role = mem_obj.metadata.get('role')

                        # Fallback to content-based role detection if metadata role is missing or unclear
                        if role not in ["user", "assistant"]:
                            if "[user]" in msg_content_str.lower():
                                role = "user"
                            elif "[assistant]" in msg_content_str.lower():
                                role = "assistant"

                        # Limit length of each message in history
                        if len(msg_content_str) > 150:
                            msg_content_str = msg_content_str[:150] + "..."

                        if role == "user":
                            prompt += f"{turns_to_display + 1}. 用户: {msg_content_str.replace('[user] ', '', 1)}\n"
                            turns_to_display += 1
                        elif role == "assistant":
                            prompt += f"{turns_to_display + 1}. 助手: {msg_content_str.replace('[assistant] ', '', 1)}\n"
                            turns_to_display += 1
                        # else: messages with unknown roles after sniffing are skipped for history

                prompt += "\n"

            # 添加与当前问题直接相关的记忆
            if topic_related_memories:
                prompt += "与当前问题直接相关的内容：\n"

                # 将记忆对象转换为内容字符串
                topic_related_contents = []
                for memory in topic_related_memories:
                    if hasattr(memory, 'content'):
                        content = str(memory.content)
                        # 限制长度
                        if len(content) > 150:
                            content = content[:150] + "..."
                        topic_related_contents.append(content)

                # 最多添加3条相关内容
                for i, content in enumerate(topic_related_contents[:3]):
                    if "[user]" in content:
                        prompt += f"{i+1}. 用户曾问: {content.replace('[user] ', '')}\n"
                    elif "[assistant]" in content:
                        prompt += f"{i+1}. 助手曾答: {content.replace('[assistant] ', '')}\n"
                    else:
                        prompt += f"{i+1}. {content}\n"

                prompt += "\n"

            # 添加与概念相关的记忆
            if concept_related_memories:
                prompt += "相关概念解释：\n"

                # 将记忆对象转换为内容字符串
                concept_related_contents = []
                for memory in concept_related_memories:
                    if hasattr(memory, 'content'):
                        content = str(memory.content)

                        # 检查是否包含代码块
                        if "```" in content:
                            # 提取代码块
                            code_start = content.find("```")
                            if code_start != -1:
                                code_end = content.find("```", code_start + 3)
                                if code_end != -1:
                                    code = content[code_start:code_end + 3]
                                    if len(code) > 200:
                                        code = code[:200] + "...\n```"
                                    concept_related_contents.append(code)
                                    continue

                        # 如果没有代码块或提取失败，限制长度
                        if len(content) > 150:
                            content = content[:150] + "..."
                        concept_related_contents.append(content)

                # 最多添加3条概念解释
                for i, content in enumerate(concept_related_contents[:3]):
                    if "[assistant]" in content:
                        prompt += f"{i+1}. {content.replace('[assistant] ', '')}\n"
                    else:
                        prompt += f"{i+1}. {content}\n"

                prompt += "\n"

            # 添加连贯性指导
            prompt += "以上记忆信息仅作为参考，为保障对话的连贯性需要有近及远进行回溯，但务必使用记忆时要判断和当前问题的相关性，如果用户信息没有明确支持具体的题目号（如XX竞赛题或PXXXX），或语意判断带有明显的追问色彩（如那么这道题该如何如何，我应该怎么修改等等之类）。则表明需要从最近一两条信息中的主题继续延续对话\n\n"

        prompt += "请根据以上信息回答C++编程问题。要求：\n"
        prompt += "1. 直接回答问题，不做无关扩展\n"
        prompt += "2. 使用专业但易懂的语言，避免过度使用emoji\n"
        prompt += "3. 确保回复不超过2048字节（约600-700中文字）\n"
        prompt += "4. 如需提供代码，使用简单易懂的实现方式，使用缩进而非代码块标记\n"
        prompt += "5. 分步骤解释时保持简洁\n"
        #prompt += "6. 如果学生之前讨论过某个话题，避免重复已解释的内容\n"
        prompt += "7. 使用适当的类比解释复杂概念，但保持专业性\n"
        prompt += "8. 保持对话的连贯性，参考之前的交流内容\n"
        prompt += "9. 使用纯文本格式回复，不要使用Markdown格式\n"
        prompt += "10. 不要使用粗体、斜体等Markdown格式，使用普通文本\n"
        prompt += "11. 使用数字和字母编号来组织内容，使用空行分隔段落\n"
        prompt += "12. 生成代码时优先使用namespace，比如使用标准模板库时避免出现（std::）这样的前缀\n"

        # 记录最终提示的长度
        logger.debug(f"Final prompt length for user {user_id}: {len(prompt)} characters")
        logger.info(f"Final prompt for user {user_id}: \n\n{prompt}")

        return prompt


# 单元测试代码
if __name__ == "__main__" and os.environ.get('RUN_EXAMPLE'):
    import unittest
    from unittest.mock import patch, MagicMock, AsyncMock

    class TeachingAssistantAgentTest(unittest.TestCase):
        @patch('autogen_ext.models.openai.OpenAIChatCompletionClient')
        @patch('agents.dev_team.crawler_engineer_agent.CrawlerEngineerAgent')
        @patch('utils.memory.multi_user_memory.MultiUserMemory')
        def setUp(self, mock_memory, mock_crawler, mock_client):
            # 设置模拟对象
            self.mock_memory = mock_memory.return_value
            # 不再需要模拟_set_compression_prompt，因为我们已经处理了这种情况
            self.mock_memory.add = AsyncMock()
            self.mock_memory.query = AsyncMock(return_value=MagicMock(results=[]))

            self.mock_crawler = mock_crawler.return_value
            self.mock_crawler._crawl_and_parsing_result = AsyncMock(return_value={
                "title": "测试题目",
                "problem_id": "P1000",
                "description": "这是一个测试题目描述",
                "input_format": "输入格式",
                "output_format": "输出格式",
                "examples": [
                    {
                        "input_example": "示例输入",
                        "output_example": "示例输出",
                        "explanation": "示例解释"
                    }
                ]
            })

            self.mock_client = mock_client.return_value
            self.mock_client.acomplete = AsyncMock(return_value=MagicMock(content="这是一个测试回复"))

            # 创建代理实例，但使用patch来避免实际初始化记忆系统
            with patch.object(TeachingAssistantAgent, '_init_memory_system'):
                self.agent = TeachingAssistantAgent(
                    name="teaching_assistant",
                    model_client=self.mock_client,
                    crawler_agent=self.mock_crawler
                )
                # 手动设置记忆系统
                self.agent.memory = self.mock_memory

            # 设置日志
            logging.basicConfig(level=logging.DEBUG)

        def test_extract_problem_id(self):
            # 测试提取题目编号
            self.assertEqual(self.agent._extract_problem_id("请帮我解答P1000这道题"), "P1000")
            self.assertEqual(self.agent._extract_problem_id("我在做P1234，遇到了问题"), "P1234")
            self.assertIsNone(self.agent._extract_problem_id("这道题很难"))
            self.assertIsNone(self.agent._extract_problem_id("P123太短了"))  # 不匹配太短的ID

        async def test_fetch_problem_info(self):
            # 测试抓取题目信息
            result = await self.agent._fetch_problem_info("P1000")
            self.assertIsNotNone(result)
            self.assertEqual(result["title"], "测试题目")
            self.assertEqual(result["problem_id"], "P1000")

            # 测试异常情况
            self.mock_crawler._crawl_and_parsing_result = AsyncMock(side_effect=Exception("测试异常"))
            result = await self.agent._fetch_problem_info("P1000")
            self.assertIsNone(result)

        async def test_add_to_memory(self):
            # 测试添加记忆
            await self.agent._add_to_memory("test_user", "测试消息", "user")
            self.mock_memory.add.assert_called_once()

            # 验证调用参数
            args, _ = self.mock_memory.add.call_args
            self.assertEqual(args[1], "test_user")  # 验证用户ID
            self.assertIn("测试消息", str(args[0].content))  # 验证消息内容

        async def test_handle_user_message(self):
            # 测试处理用户消息
            response = await self.agent.handle_user_message("test_user", "请帮我解答P1000")
            self.assertEqual(response, "这是一个测试回复")

            # 验证调用
            self.mock_memory.add.assert_called()
            self.mock_memory.query.assert_called_once()
            self.mock_crawler._crawl_and_parsing_result.assert_called_once()
            self.mock_client.acomplete.assert_called_once()

        def test_build_response_prompt(self):
            # 测试构建回复提示
            problem_info = {
                "title": "测试题目",
                "problem_id": "P1000",
                "description": "这是一个测试题目描述",
                "input_format": "输入格式",
                "output_format": "输出格式",
                "examples": [
                    {
                        "input_example": "示例输入",
                        "output_example": "示例输出",
                        "explanation": "示例解释"
                    }
                ]
            }

            memory_results = MagicMock()
            memory_results.results = [
                MagicMock(content="记忆内容1"),
                MagicMock(content="记忆内容2")
            ]

            prompt = self.agent._build_response_prompt("test_user", "测试问题", problem_info, memory_results)

            # 验证提示内容
            self.assertIn("测试问题", prompt)
            self.assertIn("测试题目", prompt)
            self.assertIn("P1000", prompt)
            self.assertIn("这是一个测试题目描述", prompt)
            self.assertIn("示例输入", prompt)
            self.assertIn("示例输出", prompt)
            self.assertIn("记忆内容1", prompt)
            self.assertIn("记忆内容2", prompt)

    # 运行单元测试
    if sys.platform != 'win32' and not os.environ.get('RUN_EXAMPLE'):  # 避免在Windows上运行asyncio测试
        unittest.main()

# 示例代码：使用TeachingAssistantAgent处理多用户场景
if __name__ == "__main__":
    import shutil

    async def run_teaching_assistant_example():
        """运行教学助手多用户示例"""
        print("初始化教学助手代理...")

        try:
            # 获取配置
            config = get_config()
            print(f"获取配置成功: {config.keys()}")

            # 创建模型客户端
            print("创建模型客户端...")
            model_client = OpenAIChatCompletionClient(
                model=config['agents']['memory_system']['agent_model_name'],
                api_key=config['agents']['memory_system']['agent_model_api_key'],
                model_info={
                    "vision": False,
                    "function_calling": True,
                    "json_output": True,
                    "family": "unknown",
                }
            )
            print("模型客户端创建成功")

            # 初始化爬虫工程师代理
            print("初始化爬虫工程师代理...")
            crawler_agent = CrawlerEngineerAgent()
            print("爬虫工程师代理初始化成功")

            # 初始化教学助手代理
            print("初始化教学助手代理...")
            teaching_assistant = TeachingAssistantAgent(
                name="teaching_assistant_example",
                model_client=model_client,
                crawler_agent=crawler_agent
            )
            print("教学助手代理初始化成功")

            # 直接设置model_client属性，以确保它存在
            if not hasattr(teaching_assistant, 'model_client') or teaching_assistant.model_client is None:
                print("手动设置model_client属性")
                teaching_assistant.model_client = model_client

            # 定义三个不同的用户
            users = {
                "user_p1005": {
                    "id": "example_user_001",
                    "questions": [
                        "你能帮我解答洛谷P1005这道题吗？我不太理解题目要求。",
                        "这道题目的核心算法是什么？",
                        "可以按照题目的描述，一步一步给我讲解下解题思路吗？",
                        "如何处理这道题中的大数问题？",
                        "最后，你能帮我归纳总结今天我们交流的内容吗？"
                    ]
                },
                "user_p1003": {
                    "id": "example_user_002",
                    "questions": [
                        "请帮我分析一下洛谷P1010这道题。",
                        "这道题目需要用什么数据结构？",
                        "如何优化算法复杂度？",
                        "有没有类似的题目可以练习？",
                        "最后，你能帮我归纳总结今天我们交流的内容吗？"
                    ]
                },
                "user_dfs": {
                    "id": "example_user_003",
                    "questions": [
                        "Python中深度优先搜索算法怎么实现？",
                        "DFS和BFS有什么区别？",
                        "如何避免DFS中的栈溢出问题？",
                        "结合上面我们交流的内容，你能帮我解答洛谷P1032这道题吗？",
                        "最后，你能帮我归纳总结今天我们交流的内容吗？"
                    ]
                }
            }

            # 交替处理每个用户的问题，模拟多用户同时交流
            for question_index in range(5):  # 每个用户有4个问题
                for user_type, user_info in users.items():
                    user_id = user_info["id"]
                    question = user_info["questions"][question_index]

                    print(f"\n用户 {user_id} (类型: {user_type}) - 问题 {question_index+1}: {question}")
                    print("-" * 50)

                    try:
                        # 使用教学助手处理问题
                        response = await teaching_assistant.handle_user_message(
                            user_id=user_id,
                            content=question
                        )

                        print(f"教学助手回复:\n{response}")
                    except Exception as e:
                        print(f"处理问题时出错: {e}")
                        import traceback
                        traceback.print_exc()

                    print("=" * 80)

            # 清理记忆数据库路径
            print("\n清理记忆数据库...")
            try:
                # 获取记忆系统基础路径
                base_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "../..", "data", "memories")

                # 检查路径是否存在
                if os.path.exists(base_path):
                    # 列出所有用户目录
                    for user_dir in os.listdir(base_path):
                        user_path = os.path.join(base_path, user_dir)
                        if os.path.isdir(user_path):
                            print(f"删除用户记忆目录: {user_path}")
                            shutil.rmtree(user_path)

                    print("记忆数据库清理完成")
                else:
                    print(f"记忆基础路径不存在: {base_path}")
            except Exception as e:
                print(f"清理记忆数据库时出错: {e}")
                import traceback
                traceback.print_exc()

        except Exception as e:
            print(f"初始化或运行过程中出错: {e}")
            import traceback
            traceback.print_exc()

    # 运行示例
    print("运行教学助手多用户示例...")
    asyncio.run(run_teaching_assistant_example())
