#!/usr/bin/env python3
"""
测试记忆修复效果的脚本
"""

import asyncio
import logging
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from agents.customer_service_team.teaching_assistant_agent import TeachingAssistantAgent

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_memory_pairing():
    """测试记忆配对功能"""
    print("开始测试记忆配对功能...")
    
    # 创建教学助手代理
    agent = TeachingAssistantAgent()
    
    # 测试用户ID
    test_user_id = "test_user_memory_fix"
    
    # 模拟一系列对话
    test_conversations = [
        ("用户", "你好，我想问一下P1005这道题"),
        ("助手", "你好！P1005是洛谷的一道经典题目。这道题考查的是数学计算和字符串处理。"),
        ("用户", "能详细解释一下解题思路吗？"),
        ("助手", "当然可以。这道题的核心思路是：1. 理解题目要求 2. 设计算法 3. 实现代码"),
        ("用户", "如何保障空间复杂度的要求，不是时间复杂度"),
        ("助手", "关于空间复杂度优化，有几个关键点：1. 避免使用额外的大数组 2. 复用变量 3. 使用常数空间算法")
    ]
    
    print(f"模拟 {len(test_conversations)} 轮对话...")
    
    # 逐一处理对话
    for i, (role, content) in enumerate(test_conversations, 1):
        print(f"\n--- 第 {i} 轮对话 ---")
        print(f"{role}: {content}")
        
        if role == "用户":
            # 处理用户消息
            response = await agent.handle_user_message(
                user_id=test_user_id,
                content=content
            )
            print(f"助手回复: {response[:100]}...")
        else:
            # 直接添加助手消息到记忆（模拟）
            await agent._add_to_memory(test_user_id, content, "assistant")
    
    print("\n--- 测试记忆检索 ---")
    
    # 测试记忆检索
    final_response = await agent.handle_user_message(
        user_id=test_user_id,
        content="重新给出代码实现"
    )
    
    print(f"最终回复: {final_response}")
    
    print("\n测试完成！请检查日志中的对话配对情况。")

async def main():
    """主函数"""
    try:
        await test_memory_pairing()
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
