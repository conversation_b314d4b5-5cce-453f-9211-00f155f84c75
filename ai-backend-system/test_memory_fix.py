#!/usr/bin/env python3
"""
测试记忆修复效果的脚本
"""

import asyncio
import logging
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from agents.customer_service_team.teaching_assistant_agent import TeachingAssistantAgent

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_memory_retrieval():
    """测试记忆检索功能"""
    print("开始测试记忆检索功能...")

    # 创建教学助手代理
    agent = TeachingAssistantAgent()

    # 测试用户ID
    test_user_id = "chieh.chu"

    print("\n--- 测试记忆检索 ---")

    # 测试记忆检索
    response = await agent.handle_user_message(
        user_id=test_user_id,
        content="如何保障空间复杂度的要求，不是时间复杂度"
    )

    print(f"回复: {response}")

    print("\n测试完成！请检查日志中的记忆检索情况。")

async def main():
    """主函数"""
    try:
        await test_memory_retrieval()
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
